import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { UserInfoModal } from '../UserInfoModal';

// Mock hooks
vi.mock('@/hooks/useWidgets', () => ({
  useWidgets: () => ({
    widgets: [],
    isLoading: false,
  }),
}));

// Mock Clerk
vi.mock('@clerk/nextjs', () => ({
  useAuth: () => ({
    isSignedIn: true,
    getToken: vi.fn().mockResolvedValue('mock-token'),
  }),
  useUser: () => ({
    user: {
      firstName: 'John',
      lastName: 'Doe',
      primaryEmailAddress: { emailAddress: '<EMAIL>' },
      primaryPhoneNumber: { phoneNumber: '+44123456789' },
      update: vi.fn().mockResolvedValue({}),
    },
    isLoaded: true,
  }),
  useClerk: () => ({
    user: {
      firstName: 'John',
      lastName: 'Doe',
      primaryEmailAddress: { emailAddress: '<EMAIL>' },
      primaryPhoneNumber: { phoneNumber: '+44123456789' },
    },
  }),
}));

vi.mock('@/components/Modal', () => ({
  Modal: ({ children, open, onClose, title }: any) =>
    open ? (
      <div data-testid="mock-modal">
        <div data-testid="modal-title">{title}</div>
        {children}
        <button onClick={onClose} data-testid="close-button">
          Close
        </button>
      </div>
    ) : null,
}));

// Mock PersonalizationCard
vi.mock('@/components/PersonalizationCard', () => ({
  PersonalizationCard: ({ fields, onEdit, onSave, onCancel, isEditing }: any) => (
    <div data-testid="personalization-card">
      {fields.map((field: any) => (
        <div key={field.id} data-testid={`field-${field.id}`}>
          <span data-testid={`field-label-${field.id}`}>{field.label}</span>
          <span data-testid={`field-value-${field.id}`}>{field.value}</span>
          {field.editable && !isEditing && (
            <button onClick={() => onEdit(field.id)} data-testid={`edit-${field.id}`}>
              Edit
            </button>
          )}
        </div>
      ))}
      {isEditing && (
        <div>
          <button onClick={onSave} data-testid="save-button">
            Save
          </button>
          <button onClick={onCancel} data-testid="cancel-button">
            Cancel
          </button>
        </div>
      )}
    </div>
  ),
}));

vi.mock('@/components/Button', () => ({
  Button: ({ children, onClick, state, disabled }: any) => (
    <button onClick={onClick} disabled={disabled || state === 'DISABLED'} data-testid="mock-button">
      {children}
    </button>
  ),
}));

vi.mock('../components/FirstNameField', () => ({
  FirstNameField: ({ initialValue, onSave, isEditing, onEditStart, onEditEnd }: any) => (
    <div data-testid="first-name-field">
      <span data-testid="first-name-value">{initialValue}</span>
      {!isEditing && (
        <button onClick={() => onEditStart('firstName')} data-testid="edit-first-name">
          Edit
        </button>
      )}
      {isEditing && (
        <div>
          <input data-testid="first-name-input" defaultValue={initialValue} />
          <button onClick={() => onSave('NewFirstName')} data-testid="save-first-name">
            Save
          </button>
          <button onClick={() => onEditEnd('firstName')} data-testid="cancel-first-name">
            Cancel
          </button>
        </div>
      )}
    </div>
  ),
}));

vi.mock('../components/LastNameField', () => ({
  LastNameField: ({ initialValue, onSave, isEditing, onEditStart, onEditEnd }: any) => (
    <div data-testid="last-name-field">
      <span data-testid="last-name-value">{initialValue}</span>
      {!isEditing && (
        <button onClick={() => onEditStart('lastName')} data-testid="edit-last-name">
          Edit
        </button>
      )}
      {isEditing && (
        <div>
          <input data-testid="last-name-input" defaultValue={initialValue} />
          <button onClick={() => onSave('NewLastName')} data-testid="save-last-name">
            Save
          </button>
          <button onClick={() => onEditEnd('lastName')} data-testid="cancel-last-name">
            Cancel
          </button>
        </div>
      )}
    </div>
  ),
}));

describe('UserInfoModal Component', () => {
  const defaultProps = {
    open: true,
    onClose: vi.fn(),
    readOnly: false,
    inline: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders when open is true', () => {
      render(<UserInfoModal {...defaultProps} />);
      expect(screen.getByTestId('mock-modal')).toBeInTheDocument();
      expect(screen.getByTestId('modal-title')).toHaveTextContent('Your details');
    });

    it('does not render when open is false', () => {
      render(<UserInfoModal {...defaultProps} open={false} />);
      expect(screen.queryByTestId('mock-modal')).not.toBeInTheDocument();
    });

    it('renders PersonalizationCard with user fields', () => {
      render(<UserInfoModal {...defaultProps} />);
      expect(screen.getByTestId('personalization-card')).toBeInTheDocument();
    });

    it('renders Continue to chat button', () => {
      render(<UserInfoModal {...defaultProps} />);
      expect(screen.getByText('Continue to chat')).toBeInTheDocument();
    });
  });

  describe('Field Editing', () => {
    it('allows editing first name field', async () => {
      const user = userEvent.setup();
      render(<UserInfoModal {...defaultProps} />);

      const editButton = screen.getByTestId('edit-first-name');
      await user.click(editButton);

      expect(screen.getByTestId('first-name-input')).toBeInTheDocument();
      expect(screen.getByTestId('save-first-name')).toBeInTheDocument();
      expect(screen.getByTestId('cancel-first-name')).toBeInTheDocument();
    });

    it('allows editing last name field', async () => {
      const user = userEvent.setup();
      render(<UserInfoModal {...defaultProps} />);

      const editButton = screen.getByTestId('edit-last-name');
      await user.click(editButton);

      expect(screen.getByTestId('last-name-input')).toBeInTheDocument();
      expect(screen.getByTestId('save-last-name')).toBeInTheDocument();
      expect(screen.getByTestId('cancel-last-name')).toBeInTheDocument();
    });

    it('saves first name changes', async () => {
      const user = userEvent.setup();
      render(<UserInfoModal {...defaultProps} />);

      const editButton = screen.getByTestId('edit-first-name');
      await user.click(editButton);

      const saveButton = screen.getByTestId('save-first-name');
      await user.click(saveButton);

      expect(screen.queryByTestId('first-name-input')).not.toBeInTheDocument();
    });

    it('cancels first name editing', async () => {
      const user = userEvent.setup();
      render(<UserInfoModal {...defaultProps} />);

      const editButton = screen.getByTestId('edit-first-name');
      await user.click(editButton);

      const cancelButton = screen.getByTestId('cancel-first-name');
      await user.click(cancelButton);

      expect(screen.queryByTestId('first-name-input')).not.toBeInTheDocument();
    });
  });

  describe('Modal Interactions', () => {
    it('calls onClose when close button is clicked', async () => {
      const user = userEvent.setup();
      render(<UserInfoModal {...defaultProps} />);

      const closeButton = screen.getByTestId('close-button');
      await user.click(closeButton);

      expect(defaultProps.onClose).toHaveBeenCalled();
    });

    it('calls onClose when Continue to chat is clicked', async () => {
      const user = userEvent.setup();
      render(<UserInfoModal {...defaultProps} />);

      const continueButton = screen.getByText('Continue to chat');
      await user.click(continueButton);

      expect(defaultProps.onClose).toHaveBeenCalled();
    });
  });

  describe('Read-only Mode', () => {
    it('renders in read-only mode when readOnly is true', () => {
      render(<UserInfoModal {...defaultProps} readOnly={true} />);

      expect(screen.getByTestId('mock-modal')).toBeInTheDocument();
    });
  });

  describe('Loading State', () => {
    it('shows loading state when user is not loaded', () => {
      vi.mocked(require('@clerk/nextjs').useUser).mockReturnValue({
        user: null,
        isLoaded: false,
      });

      render(<UserInfoModal {...defaultProps} />);

      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });
  });

  describe('Guest User Handling', () => {
    it('handles guest user conversion', () => {
      render(<UserInfoModal {...defaultProps} />);

      expect(screen.getByTestId('mock-modal')).toBeInTheDocument();
    });
  });

  describe('Field Value Synchronization', () => {
    it('prevents value reset during editing', async () => {
      const user = userEvent.setup();
      render(<UserInfoModal {...defaultProps} />);

      const editButton = screen.getByTestId('edit-first-name');
      await user.click(editButton);

      expect(screen.getByTestId('first-name-input')).toBeInTheDocument();

      expect(screen.getByTestId('save-first-name')).toBeInTheDocument();
      expect(screen.getByTestId('cancel-first-name')).toBeInTheDocument();
    });

    it('allows value updates when not editing', () => {
      const { rerender } = render(<UserInfoModal {...defaultProps} />);

      expect(screen.getByTestId('first-name-value')).toHaveTextContent('John');

      vi.mocked(require('@clerk/nextjs').useUser).mockReturnValue({
        user: {
          firstName: 'UpdatedJohn',
          lastName: 'UpdatedDoe',
          primaryEmailAddress: { emailAddress: '<EMAIL>' },
          primaryPhoneNumber: { phoneNumber: '+44987654321' },
          update: vi.fn().mockResolvedValue({}),
        },
        isLoaded: true,
      });

      rerender(<UserInfoModal {...defaultProps} />);

      expect(screen.getByTestId('first-name-value')).toHaveTextContent('UpdatedJohn');
    });
  });

  describe('Error Handling', () => {
    it('handles save errors gracefully', async () => {
      const user = userEvent.setup();

      vi.mocked(require('@clerk/nextjs').useUser).mockReturnValue({
        user: {
          firstName: 'John',
          lastName: 'Doe',
          primaryEmailAddress: { emailAddress: '<EMAIL>' },
          primaryPhoneNumber: { phoneNumber: '+44123456789' },
          update: vi.fn().mockRejectedValue(new Error('Update failed')),
        },
        isLoaded: true,
      });

      render(<UserInfoModal {...defaultProps} />);

      const editButton = screen.getByTestId('edit-first-name');
      await user.click(editButton);

      const saveButton = screen.getByTestId('save-first-name');
      await user.click(saveButton);

      expect(screen.getByTestId('mock-modal')).toBeInTheDocument();
    });
  });
});
