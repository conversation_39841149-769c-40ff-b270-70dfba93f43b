# MultiFieldInput Component Tests

This directory contains comprehensive unit tests for the MultiFieldInput component and its associated hooks.

## Test Files

### 1. `MultiFieldInput.test.tsx`
Main unit tests for the MultiFieldInput component covering:
- **Basic Rendering**: Default props, custom titles, placeholders, className application
- **Phone Input Type**: Phone number formatting, validation, input handling
- **Email Input Type**: Email input validation and handling
- **Access Instruction Type**: Textarea rendering and input handling
- **Address Input Type**: Select mode, manual mode, address field handling
- **State Management**: Disabled, loading, error states
- **Save Functionality**: Save button behavior, custom save text, hide save button
- **Styling and Layout**: Compact mode, hidden borders, hidden titles
- **Options and Dropdown**: Option rendering, selection, manual entry switching
- **Value Synchronization**: External value updates, address object handling
- **Keyboard Navigation**: Enter key, Escape key handling
- **Validation and Error Handling**: Error display, error styling, error clearing
- **Accessibility**: ARIA labels, keyboard navigation, error associations

### 2. `MultiFieldInput.snapshot.test.tsx`
Snapshot tests to ensure UI consistency across different configurations:
- Default states for all input types
- Various prop combinations
- Error states, disabled states, loading states
- Different styling options (compact, hidden border, etc.)
- Address input with different modes and values
- Custom save button text and children

### 3. `MultiFieldInput.integration.test.tsx`
Integration tests focusing on real component interactions:
- **Address Autocomplete Integration**: Search triggering, result display, selection
- **Manual Address Entry**: Individual field updates, complete address handling
- **Phone Number Integration**: Formatting, verification flow
- **Email Integration**: Input handling, verification flow
- **Save Functionality**: Saving different value types
- **Error Handling**: Error display and verification error handling

### 4. `useAddressAutocomplete.test.ts`
Unit tests for the address autocomplete hook:
- Hook initialization and state management
- Address search functionality with debouncing
- Result processing and option mapping
- Error handling for failed searches
- Edge cases (empty results, invalid data formats)
- Dependency change handling

### 5. `useVerificationModal.test.ts`
Unit tests for the verification modal hook:
- Hook initialization and state management
- **Code Sending**: Success/failure handling, error states
- **Code Verification**: Success/failure handling, error states
- **Modal Management**: Opening, closing, state reset
- **Digit Handling**: Individual digit input, paste functionality
- **Resend Countdown**: Timer functionality, enable/disable states
- **Keyboard Navigation**: Arrow keys, backspace, digit input

## Test Coverage

The tests cover:
- ✅ All component props and their effects
- ✅ All input types (phone, email, address, access instruction)
- ✅ Both address modes (select and manual)
- ✅ Phone and email verification flows
- ✅ Error handling and validation
- ✅ Accessibility features
- ✅ Keyboard navigation
- ✅ State management and synchronization
- ✅ Hook functionality and edge cases
- ✅ Integration between component and hooks

## Running Tests

To run all MultiFieldInput tests:
```bash
npm test -- src/components/MultiFieldInput/__tests__/
```

To run specific test files:
```bash
# Unit tests
npm test -- src/components/MultiFieldInput/__tests__/MultiFieldInput.test.tsx

# Snapshot tests
npm test -- src/components/MultiFieldInput/__tests__/MultiFieldInput.snapshot.test.tsx

# Integration tests
npm test -- src/components/MultiFieldInput/__tests__/MultiFieldInput.integration.test.tsx

# Hook tests
npm test -- src/components/MultiFieldInput/__tests__/useAddressAutocomplete.test.ts
npm test -- src/components/MultiFieldInput/__tests__/useVerificationModal.test.ts
```

## Mocking Strategy

The tests use comprehensive mocking for:
- **External Dependencies**: Clerk authentication, widgets hook, breakpoint utils
- **Child Components**: Modal and Button components for focused testing
- **Hooks**: Custom hooks are mocked in unit tests, real in integration tests
- **Timers**: Fake timers for testing countdown functionality
- **Network Calls**: Mock functions for address search and verification APIs

## Test Patterns

The tests follow these patterns:
- **Arrange-Act-Assert**: Clear test structure
- **User-Centric Testing**: Using `@testing-library/user-event` for realistic interactions
- **Async Handling**: Proper `waitFor` and `act` usage for async operations
- **Mock Cleanup**: `beforeEach` cleanup for consistent test state
- **Descriptive Test Names**: Clear test descriptions for easy understanding

## Maintenance

When updating the MultiFieldInput component:
1. Update relevant unit tests for new functionality
2. Add new snapshot tests for UI changes
3. Update integration tests for new interaction flows
4. Ensure hook tests cover new hook functionality
5. Update this README if test structure changes
