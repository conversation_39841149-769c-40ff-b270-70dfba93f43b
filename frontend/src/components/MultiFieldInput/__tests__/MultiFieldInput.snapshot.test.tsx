import React from 'react';
import { render } from '@testing-library/react';
import { vi } from 'vitest';
import { MultiFieldInput } from '../MultiFieldInput';
import { MultiFieldInputType, MultiFieldInputMode } from '../MultiFieldInput.types';

// Mock hooks
vi.mock('@/hooks/useWidgets', () => ({
  useWidgets: () => ({
    findAddresses: vi.fn(),
  }),
}));

vi.mock('@clerk/nextjs', () => ({
  useAuth: () => ({
    getToken: vi.fn().mockResolvedValue('mock-token'),
    isSignedIn: true,
  }),
  useClerk: () => ({
    openUserProfile: vi.fn(),
  }),
}));

vi.mock('@/utils/breakpointUtils', () => ({
  useBreakpoint: () => ({
    isMobile: false,
  }),
}));

vi.mock('../useAddressAutocomplete', () => ({
  useAddressAutocomplete: () => ({
    localOptions: [
      { value: 'option1', label: 'Mock Address Option 1' },
      { value: 'option2', label: 'Mock Address Option 2' },
    ],
    setLocalOptions: vi.fn(),
    debouncedFindAddresses: vi.fn(),
  }),
}));

vi.mock('../useVerificationModal', () => ({
  useVerificationModal: () => ({
    verificationState: 'initial',
    verificationCode: '',
    verificationDigits: ['', '', '', '', '', ''],
    targetValue: '',
    verificationError: '',
    resendCountdown: 0,
    isResendDisabled: false,
    isModalOpen: false,
    codeInputRefs: { current: [] },
    setVerificationDigits: vi.fn(),
    setVerificationCode: vi.fn(),
    setTargetValue: vi.fn(),
    setIsModalOpen: vi.fn(),
    handleSendCode: vi.fn(),
    handleVerifyCode: vi.fn(),
    handleCloseModal: vi.fn(),
    handleDigitChange: vi.fn(),
    handleDigitKeyDown: vi.fn(),
    handleDigitFocus: vi.fn(),
    handleDigitInput: vi.fn(),
    handlePaste: vi.fn(),
  }),
}));

// Mock Modal component
vi.mock('../../Modal', () => ({
  Modal: ({ children, open, onClose, title }: any) =>
    open ? (
      <div data-testid="mock-modal" role="dialog">
        <div>{title}</div>
        <div>{children}</div>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null,
}));

// Mock Button component
vi.mock('../../Button', () => ({
  Button: ({ children, onClick, disabled, ...props }: any) => (
    <button data-testid="mock-button" onClick={onClick} disabled={disabled} {...props}>
      {children}
    </button>
  ),
}));

const defaultProps = {
  type: MultiFieldInputType.PHONE,
  value: '',
  onChange: vi.fn(),
  onSave: vi.fn(),
};

describe('MultiFieldInput Snapshots', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('matches snapshot with default phone input', () => {
    const { container } = render(<MultiFieldInput {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with custom title and placeholder', () => {
    const { container } = render(
      <MultiFieldInput
        {...defaultProps}
        title="Custom Phone Title"
        placeholder="Enter phone number"
      />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with email input type', () => {
    const { container } = render(
      <MultiFieldInput type={MultiFieldInputType.EMAIL} value="" onChange={vi.fn()} />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with access instruction type', () => {
    const { container } = render(
      <MultiFieldInput
        type={MultiFieldInputType.ACCESS_INSTRUCTION}
        value=""
        onChange={vi.fn()}
      />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with address input in select mode', () => {
    const { container } = render(
      <MultiFieldInput
        type={MultiFieldInputType.ADDRESS}
        initialMode={MultiFieldInputMode.SELECT}
        value=""
        onChange={vi.fn()}
      />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with address input in manual mode', () => {
    const { container } = render(
      <MultiFieldInput
        type={MultiFieldInputType.ADDRESS}
        initialMode={MultiFieldInputMode.MANUAL}
        value={{ line1: '', line2: '', city: '', postcode: '' }}
        onChange={vi.fn()}
      />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with address input with values', () => {
    const { container } = render(
      <MultiFieldInput
        type={MultiFieldInputType.ADDRESS}
        initialMode={MultiFieldInputMode.MANUAL}
        value={{
          line1: '123 Main Street',
          line2: 'Apartment 4B',
          city: 'London',
          postcode: 'SW1A 1AA',
        }}
        onChange={vi.fn()}
      />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with error state', () => {
    const { container } = render(
      <MultiFieldInput {...defaultProps} error="This field is required" />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with disabled state', () => {
    const { container } = render(<MultiFieldInput {...defaultProps} disabled />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with loading state', () => {
    const { container } = render(<MultiFieldInput {...defaultProps} loading />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with compact mode', () => {
    const { container } = render(<MultiFieldInput {...defaultProps} compactMode />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with hidden border', () => {
    const { container } = render(<MultiFieldInput {...defaultProps} hideBorder />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with hidden save button', () => {
    const { container } = render(<MultiFieldInput {...defaultProps} hideSaveButton />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with hidden title', () => {
    const { container } = render(
      <MultiFieldInput {...defaultProps} title="Hidden Title" hideTitle />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with custom save button text', () => {
    const { container } = render(
      <MultiFieldInput {...defaultProps} saveButtonText="Custom Save Text" />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with children', () => {
    const { container } = render(
      <MultiFieldInput {...defaultProps}>
        <div>Custom child content</div>
      </MultiFieldInput>
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with custom className', () => {
    const { container } = render(
      <MultiFieldInput {...defaultProps} className="custom-class" />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with container className', () => {
    const { container } = render(
      <MultiFieldInput {...defaultProps} containerClassName="custom-container-class" />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with address options', () => {
    const mockOptions = [
      { value: 'addr1', label: '123 Main Street, London, SW1A 1AA' },
      { value: 'addr2', label: '456 Oak Avenue, Manchester, M1 1AA' },
    ];

    const { container } = render(
      <MultiFieldInput
        type={MultiFieldInputType.ADDRESS}
        initialMode={MultiFieldInputMode.SELECT}
        options={mockOptions}
        value=""
        onChange={vi.fn()}
      />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with manual entry text', () => {
    const { container } = render(
      <MultiFieldInput
        type={MultiFieldInputType.ADDRESS}
        initialMode={MultiFieldInputMode.SELECT}
        manualEntryText="Enter address manually"
        value=""
        onChange={vi.fn()}
      />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with phone input and value', () => {
    const { container } = render(
      <MultiFieldInput type={MultiFieldInputType.PHONE} value="+441234567890" onChange={vi.fn()} />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with email input and value', () => {
    const { container } = render(
      <MultiFieldInput
        type={MultiFieldInputType.EMAIL}
        value="<EMAIL>"
        onChange={vi.fn()}
      />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with access instruction and value', () => {
    const { container } = render(
      <MultiFieldInput
        type={MultiFieldInputType.ACCESS_INSTRUCTION}
        value="Please use the side entrance and ring the bell twice."
        onChange={vi.fn()}
      />
    );
    expect(container).toMatchSnapshot();
  });
});
