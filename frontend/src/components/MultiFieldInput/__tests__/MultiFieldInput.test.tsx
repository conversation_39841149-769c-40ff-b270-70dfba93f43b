import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { MultiFieldInput } from '../MultiFieldInput';
import { MultiFieldInputType, MultiFieldInputMode } from '../MultiFieldInput.types';

// Mock hooks
vi.mock('@/hooks/useWidgets', () => ({
  useWidgets: () => ({
    findAddresses: vi.fn(),
  }),
}));

vi.mock('@clerk/nextjs', () => ({
  useAuth: () => ({
    getToken: vi.fn().mockResolvedValue('mock-token'),
    isSignedIn: true,
  }),
  useClerk: () => ({
    openUserProfile: vi.fn(),
  }),
}));

vi.mock('@/utils/breakpointUtils', () => ({
  useBreakpoint: () => ({
    isMobile: false,
  }),
}));

vi.mock('@/hooks/useAddressAutocomplete', () => ({
  useAddressAutocomplete: () => ({
    localOptions: [],
    setLocalOptions: vi.fn(),
    debouncedFindAddresses: vi.fn(),
  }),
}));

vi.mock('@/hooks/useVerificationModal', () => ({
  useVerificationModal: () => ({
    verificationState: 'initial',
    verificationCode: '',
    verificationDigits: ['', '', '', '', '', ''],
    targetValue: '',
    verificationError: '',
    resendCountdown: 0,
    isResendDisabled: false,
    isModalOpen: false,
    codeInputRefs: { current: [] },
    setVerificationDigits: vi.fn(),
    setVerificationCode: vi.fn(),
    setTargetValue: vi.fn(),
    setIsModalOpen: vi.fn(),
    handleSendCode: vi.fn(),
    handleVerifyCode: vi.fn(),
    handleCloseModal: vi.fn(),
    handleDigitChange: vi.fn(),
    handleDigitKeyDown: vi.fn(),
    handleDigitFocus: vi.fn(),
    handleDigitInput: vi.fn(),
    handlePaste: vi.fn(),
  }),
}));

// Mock Modal component
vi.mock('../../Modal', () => ({
  Modal: ({ children, open, onClose, title }: any) =>
    open ? (
      <div data-testid="mock-modal" role="dialog" aria-labelledby="modal-title">
        <div id="modal-title">{title}</div>
        <div>{children}</div>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null,
}));

// Mock Button component
vi.mock('../../Button', () => ({
  Button: ({ children, onClick, disabled, ...props }: any) => (
    <button data-testid="mock-button" onClick={onClick} disabled={disabled} {...props}>
      {children}
    </button>
  ),
}));

const defaultProps = {
  type: MultiFieldInputType.PHONE,
  value: '',
  onChange: vi.fn(),
  onSave: vi.fn(),
};

describe('MultiFieldInput', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders with default props', () => {
      render(<MultiFieldInput {...defaultProps} />);
      expect(screen.getByRole('textbox')).toBeInTheDocument();
    });

    it('renders with custom title', () => {
      render(<MultiFieldInput {...defaultProps} title="Custom Title" />);
      expect(screen.getByText('Custom Title')).toBeInTheDocument();
    });

    it('renders with placeholder', () => {
      render(<MultiFieldInput {...defaultProps} placeholder="Custom placeholder" />);
      expect(screen.getByPlaceholderText('Custom placeholder')).toBeInTheDocument();
    });

    it('applies custom className', () => {
      const { container } = render(<MultiFieldInput {...defaultProps} className="custom-class" />);
      expect(container.querySelector('.custom-class')).toBeInTheDocument();
    });

    it('renders children when provided', () => {
      render(
        <MultiFieldInput {...defaultProps}>
          <div data-testid="child-element">Child content</div>
        </MultiFieldInput>
      );
      expect(screen.getByTestId('child-element')).toBeInTheDocument();
    });
  });

  describe('Phone Input Type', () => {
    it('renders phone input with correct placeholder', () => {
      render(<MultiFieldInput type={MultiFieldInputType.PHONE} />);
      expect(screen.getByPlaceholderText('+44XXXXXXXXXX')).toBeInTheDocument();
    });

    it('formats phone number input correctly', async () => {
      const user = userEvent.setup();
      const onChange = vi.fn();
      render(<MultiFieldInput type={MultiFieldInputType.PHONE} onChange={onChange} />);

      const input = screen.getByRole('textbox');
      await user.type(input, '1234567890');

      expect(onChange).toHaveBeenCalledWith('+1234567890');
    });

    it('handles phone number with existing + prefix', async () => {
      const user = userEvent.setup();
      const onChange = vi.fn();
      render(<MultiFieldInput type={MultiFieldInputType.PHONE} onChange={onChange} />);

      const input = screen.getByRole('textbox');
      await user.type(input, '+441234567890');

      expect(onChange).toHaveBeenCalledWith('+441234567890');
    });
  });

  describe('Email Input Type', () => {
    it('renders email input with correct placeholder', () => {
      render(<MultiFieldInput type={MultiFieldInputType.EMAIL} />);
      expect(screen.getByPlaceholderText('Enter your email address')).toBeInTheDocument();
    });

    it('handles email input changes', async () => {
      const user = userEvent.setup();
      const onChange = vi.fn();
      render(<MultiFieldInput type={MultiFieldInputType.EMAIL} onChange={onChange} />);

      const input = screen.getByRole('textbox');
      await user.type(input, '<EMAIL>');

      expect(onChange).toHaveBeenCalledWith('<EMAIL>');
    });
  });

  describe('Access Instruction Type', () => {
    it('renders textarea for access instructions', () => {
      render(<MultiFieldInput type={MultiFieldInputType.ACCESS_INSTRUCTION} />);
      expect(screen.getByRole('textbox')).toBeInTheDocument();
      expect(screen.getByRole('textbox').tagName).toBe('TEXTAREA');
    });

    it('handles access instruction input changes', async () => {
      const user = userEvent.setup();
      const onChange = vi.fn();
      render(<MultiFieldInput type={MultiFieldInputType.ACCESS_INSTRUCTION} onChange={onChange} />);

      const textarea = screen.getByRole('textbox');
      await user.type(textarea, 'Access instructions here');

      expect(onChange).toHaveBeenCalledWith('Access instructions here');
    });
  });

  describe('Address Input Type', () => {
    it('renders in select mode by default for address type', () => {
      render(<MultiFieldInput type={MultiFieldInputType.ADDRESS} />);
      expect(screen.getByText('Select your address')).toBeInTheDocument();
    });

    it('renders manual address fields in manual mode', () => {
      render(
        <MultiFieldInput
          type={MultiFieldInputType.ADDRESS}
          initialMode={MultiFieldInputMode.MANUAL}
          value={{ line1: '', line2: '', city: '', postcode: '' }}
        />
      );

      expect(screen.getByPlaceholderText('Line 1')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Line 2')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('City')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Postcode')).toBeInTheDocument();
    });

    it('handles address field changes in manual mode', async () => {
      const user = userEvent.setup();
      const onChange = vi.fn();
      render(
        <MultiFieldInput
          type={MultiFieldInputType.ADDRESS}
          initialMode={MultiFieldInputMode.MANUAL}
          value={{ line1: '', line2: '', city: '', postcode: '' }}
          onChange={onChange}
        />
      );

      const line1Input = screen.getByPlaceholderText('Line 1');
      await user.type(line1Input, '123 Main St');

      expect(onChange).toHaveBeenCalledWith({
        line1: '123 Main St',
        line2: '',
        city: '',
        postcode: '',
      });
    });
  });

  describe('State Management', () => {
    it('handles disabled state', () => {
      render(<MultiFieldInput {...defaultProps} disabled />);
      const input = screen.getByRole('textbox');
      expect(input).toBeDisabled();
    });

    it('handles loading state', () => {
      render(<MultiFieldInput {...defaultProps} loading />);
      const input = screen.getByRole('textbox');
      expect(input).toBeDisabled();
    });

    it('displays error message when provided', () => {
      render(<MultiFieldInput {...defaultProps} error="Test error message" />);
      expect(screen.getByText('Test error message')).toBeInTheDocument();
    });
  });

  describe('Save Functionality', () => {
    it('calls onSave when save button is clicked', async () => {
      const user = userEvent.setup();
      const onSave = vi.fn();
      render(<MultiFieldInput {...defaultProps} onSave={onSave} value="test value" />);

      const saveButton = screen.getByTestId('mock-button');
      await user.click(saveButton);

      expect(onSave).toHaveBeenCalledWith('test value');
    });

    it('hides save button when hideSaveButton is true', () => {
      render(<MultiFieldInput {...defaultProps} hideSaveButton />);
      expect(screen.queryByTestId('mock-button')).not.toBeInTheDocument();
    });

    it('uses custom save button text', () => {
      render(<MultiFieldInput {...defaultProps} saveButtonText="Custom Save" />);
      expect(screen.getByText('Custom Save')).toBeInTheDocument();
    });
  });

  describe('Styling and Layout', () => {
    it('applies compact mode styling', () => {
      const { container } = render(<MultiFieldInput {...defaultProps} compactMode />);
      expect(container.querySelector('.compact')).toBeInTheDocument();
    });

    it('hides border when hideBorder is true', () => {
      const { container } = render(<MultiFieldInput {...defaultProps} hideBorder />);
      expect(container.querySelector('.noBorder')).toBeInTheDocument();
    });

    it('hides title when hideTitle is true', () => {
      render(<MultiFieldInput {...defaultProps} title="Test Title" hideTitle />);
      expect(screen.queryByText('Test Title')).not.toBeInTheDocument();
    });
  });

  describe('Options and Dropdown', () => {
    const mockOptions = [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2' },
    ];

    it('renders options when provided', () => {
      render(
        <MultiFieldInput
          type={MultiFieldInputType.ADDRESS}
          options={mockOptions}
          initialMode={MultiFieldInputMode.SELECT}
        />
      );

      expect(screen.getByText('Option 1')).toBeInTheDocument();
      expect(screen.getByText('Option 2')).toBeInTheDocument();
    });

    it('calls onSelectOption when option is selected', async () => {
      const user = userEvent.setup();
      const onSelectOption = vi.fn();
      render(
        <MultiFieldInput
          type={MultiFieldInputType.ADDRESS}
          options={mockOptions}
          onSelectOption={onSelectOption}
          initialMode={MultiFieldInputMode.SELECT}
        />
      );

      await user.click(screen.getByText('Option 1'));
      expect(onSelectOption).toHaveBeenCalledWith(mockOptions[0]);
    });

    it('shows manual entry option for address type', () => {
      render(
        <MultiFieldInput
          type={MultiFieldInputType.ADDRESS}
          initialMode={MultiFieldInputMode.SELECT}
          manualEntryText="Enter manually"
        />
      );

      expect(screen.getByText('Enter manually')).toBeInTheDocument();
    });

    it('switches to manual mode when manual entry is clicked', async () => {
      const user = userEvent.setup();
      const onSwitchToManual = vi.fn();
      render(
        <MultiFieldInput
          type={MultiFieldInputType.ADDRESS}
          initialMode={MultiFieldInputMode.SELECT}
          onSwitchToManual={onSwitchToManual}
          manualEntryText="Enter manually"
        />
      );

      await user.click(screen.getByText('Enter manually'));
      expect(onSwitchToManual).toHaveBeenCalled();
    });
  });

  describe('Value Synchronization', () => {
    it('updates input value when external value changes', () => {
      const { rerender } = render(<MultiFieldInput {...defaultProps} value="initial" />);
      expect(screen.getByDisplayValue('initial')).toBeInTheDocument();

      rerender(<MultiFieldInput {...defaultProps} value="updated" />);
      expect(screen.getByDisplayValue('updated')).toBeInTheDocument();
    });

    it('handles address object values correctly', () => {
      const addressValue = {
        line1: '123 Main St',
        line2: 'Apt 4',
        city: 'London',
        postcode: 'SW1A 1AA',
      };

      render(
        <MultiFieldInput
          type={MultiFieldInputType.ADDRESS}
          initialMode={MultiFieldInputMode.MANUAL}
          value={addressValue}
        />
      );

      expect(screen.getByDisplayValue('123 Main St')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Apt 4')).toBeInTheDocument();
      expect(screen.getByDisplayValue('London')).toBeInTheDocument();
      expect(screen.getByDisplayValue('SW1A 1AA')).toBeInTheDocument();
    });
  });

  describe('Keyboard Navigation', () => {
    it('handles Enter key to trigger save', async () => {
      const user = userEvent.setup();
      const onSave = vi.fn();
      render(<MultiFieldInput {...defaultProps} onSave={onSave} value="test" />);

      const input = screen.getByRole('textbox');
      await user.type(input, '{enter}');

      expect(input).toBeInTheDocument();
    });

    it('handles Escape key to close dropdown', async () => {
      const user = userEvent.setup();
      render(
        <MultiFieldInput
          type={MultiFieldInputType.ADDRESS}
          initialMode={MultiFieldInputMode.SELECT}
        />
      );

      const input = screen.getByRole('textbox');
      await user.type(input, '{escape}');

      expect(input).toBeInTheDocument();
    });
  });

  describe('Validation and Error Handling', () => {
    it('displays validation error', () => {
      render(<MultiFieldInput {...defaultProps} error="Invalid input" />);
      expect(screen.getByText('Invalid input')).toBeInTheDocument();
    });

    it('applies error styling when error is present', () => {
      const { container } = render(<MultiFieldInput {...defaultProps} error="Error message" />);
      expect(container.querySelector('.error')).toBeInTheDocument();
    });

    it('clears error when input changes', async () => {
      const user = userEvent.setup();
      const { rerender } = render(<MultiFieldInput {...defaultProps} error="Error message" />);
      expect(screen.getByText('Error message')).toBeInTheDocument();

      rerender(<MultiFieldInput {...defaultProps} error="" />);
      expect(screen.queryByText('Error message')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(<MultiFieldInput {...defaultProps} />);
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('aria-label');
    });

    it('associates error messages with input', () => {
      render(<MultiFieldInput {...defaultProps} error="Error message" />);
      const input = screen.getByRole('textbox');
      const errorElement = screen.getByText('Error message');

      expect(input).toBeInTheDocument();
      expect(errorElement).toBeInTheDocument();
    });

    it('supports keyboard navigation', () => {
      render(<MultiFieldInput {...defaultProps} />);
      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('tabIndex');
    });
  });
});
